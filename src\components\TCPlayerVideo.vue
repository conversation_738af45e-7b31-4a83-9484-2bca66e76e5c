<template>
  <div class="tcplayer-container">
    <div class="video-header">
      <span class="video-title">{{ deviceName }}</span>
      <div class="video-controls">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="refreshVideo" 
          class="control-btn"
          title="刷新视频"
        />
        
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="closeVideo" 
          class="control-btn close-btn"
          title="关闭"
        />
      </div>
    </div>
    
    <div class="video-wrapper">
      <!-- TCPlayer 容器 -->
      <video
        :id="playerId"
        ref="tcPlayerVideo"
        :width="width"
        :height="height"
        preload="auto"
        playsinline
        webkit-playsinline
        x5-playsinline
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="video-loading">
        <i class="el-icon-loading"></i>
        <span>{{ loadingText }}</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="video-error">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
        <div v-if="error.includes('License')" class="license-help">
          <p>📋 如何申请TCPlayer License：</p>
          <ol>
            <li>访问 <a href="https://console.cloud.tencent.com/vcube/web?tab=player" target="_blank">腾讯云控制台</a></li>
            <li>申请"播放器 Web 端基础版 License"（免费）</li>
            <li>获取License URL并配置到组件的licenseUrl属性</li>
          </ol>
        </div>
        <div class="error-actions">
          <el-button type="primary" size="mini" @click="retryLoad">重试</el-button>
          <el-button v-if="error.includes('License')" type="success" size="mini" @click="openLicenseConsole">申请License</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TCPlayer from 'tcplayer.js'
import 'tcplayer.js/dist/tcplayer.min.css'

export default {
  name: 'TCPlayerVideo',
  props: {
    src: {
      type: String,
      required: true
    },
    deviceName: {
      type: String,
      default: '视频播放器'
    },
    width: {
      type: [String, Number],
      default: 400
    },
    height: {
      type: [String, Number],
      default: 250
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    muted: {
      type: Boolean,
      default: true
    },
    // TCPlayer License URL (需要申请)
    licenseUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      player: null,
      playerId: `tcplayer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      loading: false,
      loadingText: '视频加载中...',
      error: null,
      retryCount: 0,
      maxRetries: 3,
      isWebRTC: false
    }
  },
  mounted() {
    this.initPlayer()
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    src(newSrc) {
      if (newSrc) {
        this.initPlayer()
      }
    }
  },
  methods: {
    
    initPlayer() {
      if (!this.src) {
        this.error = '视频源URL为空'
        this.loading = false
        return
      }

      // 检查License配置
      if (!this.licenseUrl && !this.isLocalhost()) {
        this.error = '缺少TCPlayer License URL，请配置licenseUrl属性'
        this.loading = false
        console.error('TCPlayer需要License URL，请访问腾讯云控制台申请：https://console.cloud.tencent.com/vcube/web?tab=player')
        return
      }

      this.loading = true
      this.error = null
      this.isWebRTC = this.src.toLowerCase().startsWith('webrtc://')

      try {
        // 销毁现有播放器
        this.destroyPlayer()

        const playerOptions = {
          // 基础配置
          width: this.width,
          height: this.height,
          autoplay: this.autoplay,
          muted: this.muted,
          loop: false,
          preload: 'auto',

          // 视频源配置
          sources: [{
            src: this.src,
            type: this.getVideoType(this.src)
          }]
        }

        // 只在非localhost环境或有License时添加License配置
        if (this.licenseUrl) {
          playerOptions.licenseUrl = this.licenseUrl
          console.log('使用License URL:', this.licenseUrl)
        } else if (this.isLocalhost()) {
          console.log('本地开发环境，跳过License验证')
        }

        // WebRTC特殊配置
        if (this.isWebRTC) {
          playerOptions.webrtc = {
            // WebRTC相关配置
            enableAutoSwitchToFallback: true, // 启用自动降级
            fallbackUrls: this.generateFallbackUrls(this.src) // 降级URL
          }
        }

        // 播放器UI配置
        playerOptions.controls = true
        playerOptions.fluid = false
        playerOptions.responsive = true

        // 事件回调
        playerOptions.listener = (msg) => {
          this.handlePlayerEvent(msg)
        }

        console.log('初始化TCPlayer，配置:', playerOptions)

        // 使用npm导入的TCPlayer创建实例
        this.player = new TCPlayer(this.playerId, playerOptions)

        this.setupPlayerEvents()

      } catch (error) {
        console.error('初始化TCPlayer失败:', error)
        this.error = '播放器初始化失败: ' + error.message
        this.loading = false
      }
    },
    
    getVideoType(src) {
      const url = src.toLowerCase()
      
      if (url.startsWith('webrtc://')) {
        return 'application/webrtc'
      } else if (url.includes('.m3u8')) {
        return 'application/x-mpegURL'
      } else if (url.includes('.flv')) {
        return 'video/x-flv'
      } else if (url.includes('.mp4')) {
        return 'video/mp4'
      } else if (url.startsWith('rtmp://')) {
        return 'rtmp/mp4'
      }
      
      return 'video/mp4'
    },
    
    generateFallbackUrls(webrtcUrl) {
      // 为WebRTC生成降级URL
      if (!webrtcUrl.startsWith('webrtc://')) {
        return []
      }
      
      try {
        const url = new URL(webrtcUrl.replace('webrtc://', 'https://'))
        return [
          `${url.origin}${url.pathname}.flv`,
          `${url.origin}${url.pathname}/index.m3u8`,
          `${url.origin}${url.pathname}.m3u8`
        ]
      } catch (error) {
        console.error('生成降级URL失败:', error)
        return []
      }
    },
    
    isLocalhost() {
      // 检查是否为本地开发环境
      return window.location.hostname === 'localhost' ||
             window.location.hostname === '127.0.0.1' ||
             window.location.hostname === '0.0.0.0'
    },

    getDefaultLicenseUrl() {
      // 本地开发环境不需要License
      if (this.isLocalhost()) {
        return ''
      }

      // 生产环境需要配置License URL
      // 请访问 https://console.cloud.tencent.com/vcube/web?tab=player 申请
      return this.licenseUrl || ''
    },
    
    setupPlayerEvents() {
      if (!this.player) return
      
      // 播放器准备就绪
      this.player.on('ready', () => {
        console.log('TCPlayer准备就绪')
        this.loading = false
        this.player.play();
      })
      
      // 播放开始
      this.player.on('play', () => {
        console.log('视频开始播放')
        this.loading = false
        this.retryCount = 0
      })
      
      // 播放暂停
      this.player.on('pause', () => {
        console.log('视频暂停')
      })
      
      // 播放错误
      this.player.on('error', (error) => {
        console.error('TCPlayer播放错误:', error)
        this.handleVideoError()
      })
      
      // 加载开始
      this.player.on('loadstart', () => {
        this.loading = true
        this.error = null
      })
      
      // 可以播放
      this.player.on('canplay', () => {
        this.loading = false
      })
      
      // 等待数据
      this.player.on('waiting', () => {
        this.loading = true
        this.loadingText = '缓冲中...'
      })
      
      // 继续播放
      this.player.on('playing', () => {
        this.loading = false
        this.loadingText = '视频加载中...'
      })
    },
    
    handlePlayerEvent(msg) {
      console.log('TCPlayer事件:', msg)

      switch (msg.type) {
        case 'error':
          // 检查是否为License错误
          if (msg.data && msg.data.code) {
            const errorCode = msg.data.code
            console.error('TCPlayer错误码:', errorCode)

            switch (errorCode) {
              case '55':
                this.error = 'TCPlayer License缺失，请配置License URL'
                this.loading = false
                return
              case '54':
                this.error = 'TCPlayer License类型错误'
                this.loading = false
                return
              case '53':
                this.error = 'TCPlayer License时间验证失败'
                this.loading = false
                return
              default:
                this.error = `TCPlayer错误 (${errorCode}): ${msg.data.message || '未知错误'}`
            }
          }
          this.handleVideoError()
          break
        case 'webrtcStats':
          // WebRTC统计信息
          console.log('WebRTC统计:', msg.data)
          break
        default:
          break
      }
    },
    
    handleVideoError() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`视频播放失败，正在重试 (${this.retryCount}/${this.maxRetries})`)
        setTimeout(() => {
          this.retryLoad()
        }, 2000)
      } else {
        this.error = this.isWebRTC ? 
          'WebRTC视频播放失败，请检查网络或尝试其他格式' : 
          '视频播放失败，请检查网络连接或视频源'
        this.loading = false
      }
    },
    
    retryLoad() {
      this.error = null
      this.initPlayer()
    },

    openLicenseConsole() {
      window.open('https://console.cloud.tencent.com/vcube/web?tab=player', '_blank')
    },
    
    refreshVideo() {
      this.retryCount = 0
      this.initPlayer()
    },
    
    toggleFullscreen() {
      if (this.player) {
        try {
          if (this.player.isFullscreen && this.player.isFullscreen()) {
            this.player.exitFullscreen()
          } else {
            this.player.requestFullscreen()
          }
        } catch (error) {
          console.error('全屏操作失败:', error)
        }
      }
    },
    
    closeVideo() {
      this.$emit('close')
    },
    
    destroyPlayer() {
      if (this.player) {
        try {
          // 使用正确的TCPlayer销毁方法
          this.player.dispose()
          this.player = null
        } catch (error) {
          console.error('销毁TCPlayer失败:', error)
        }
      }
    },
    
    // 公共方法
    play() {
      if (this.player) {
        this.player.play()
      }
    },
    
    pause() {
      if (this.player) {
        this.player.pause()
      }
    },
    
    stop() {
      if (this.player) {
        this.player.pause()
        this.player.currentTime(0)
      }
    },
    
    setVolume(volume) {
      if (this.player) {
        this.player.volume(volume)
      }
    },
    
    mute() {
      if (this.player) {
        this.player.muted(true)
      }
    },
    
    unmute() {
      if (this.player) {
        this.player.muted(false)
      }
    }
  }
}
</script>

<style scoped>
.tcplayer-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn {
  padding: 5px;
  font-size: 16px;
  color: #606266;
  transition: color 0.3s;
}

.control-btn:hover {
  color: #409EFF;
}

.close-btn:hover {
  color: #f56c6c;
}

.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
  height: 250px;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 4px;
  min-width: 150px;
}

.video-loading i,
.video-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.video-loading span,
.video-error span {
  font-size: 14px;
  margin-bottom: 10px;
  text-align: center;
}

.license-help {
  margin: 15px 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  text-align: left;
  font-size: 12px;
}

.license-help p {
  margin: 0 0 8px 0;
  font-weight: bold;
}

.license-help ol {
  margin: 0;
  padding-left: 20px;
}

.license-help li {
  margin: 4px 0;
  line-height: 1.4;
}

.license-help a {
  color: #409EFF;
  text-decoration: underline;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.video-error .el-button {
  margin: 0;
}

/* TCPlayer容器样式 */
#tcplayer-container video {
  width: 100% !important;
  height: 250px !important;
  display: block;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .video-header {
    padding: 8px 12px;
  }

  .video-title {
    font-size: 12px;
  }

  .control-btn {
    font-size: 14px;
    padding: 3px;
  }

  .video-loading,
  .video-error {
    padding: 15px;
    min-width: 120px;
  }

  .video-loading i,
  .video-error i {
    font-size: 20px;
  }

  .video-loading span,
  .video-error span {
    font-size: 12px;
  }
}
</style>
