<template>
  <div class="debug-switch-wrapper" @click="handleClick">
    <div class="custom-switch" :class="{ 'is-checked': value, 'is-disabled': disabled }">
      <div class="switch-core">
        <div class="switch-button">
          <span class="switch-text">{{ value ? activeText : inactiveText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugSwitch',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    activeText: {
      type: String,
      default: '开启'
    },
    inactiveText: {
      type: String,
      default: '关闭'
    },
    size: {
      type: String,
      default: 'mini'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    activeColor: {
      type: String,
      default: '#409EFF'
    },
    inactiveColor: {
      type: String,
      default: '#C0CCDA'
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      const newValue = !this.value
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    }
  }
}
</script>

<style scoped>
.debug-switch-wrapper {
  display: inline-block;
  cursor: pointer;
}

.custom-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
  transition: all 0.3s;
}

.custom-switch.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.switch-core {
  width: 100%;
  height: 100%;
  background-color: #FF6B6B;
  border-radius: 13px;
  position: relative;
  transition: all 0.3s;
  overflow: hidden;
}

.custom-switch.is-checked .switch-core {
  background-color: #4FC3F7;
}

.switch-button {
  position: absolute;
  width: 32px;
  height: 22px;
  background-color: #fff;
  border-radius: 11px;
  top: 2px;
  left: 2px;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-switch.is-checked .switch-button {
  left: calc(100% - 34px);
}

.switch-text {
  font-size: 10px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1;
}

.debug-switch-wrapper:hover .custom-switch:not(.is-disabled) .switch-core {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>
